<template>
  <div id="app">
    <div class="sub-app-header">
      <h2>🎯 子应用</h2>
      <nav>
        <router-link to="/">首页</router-link>
        <router-link to="/dashboard">仪表板</router-link>
        <router-link to="/profile">个人资料</router-link>
      </nav>
    </div>
    
    <div class="sub-app-content">
      <router-view/>
    </div>
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  border: 2px solid #e74c3c;
  border-radius: 10px;
  margin: 10px;
  background: #fff;
}

.sub-app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  color: white;
  border-radius: 8px 8px 0 0;
}

.sub-app-header h2 {
  margin: 0 0 15px 0;
}

.sub-app-header nav {
  display: flex;
  gap: 15px;
}

.sub-app-header nav a {
  color: white;
  text-decoration: none;
  padding: 8px 12px;
  border-radius: 5px;
  transition: background-color 0.3s;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.sub-app-header nav a:hover,
.sub-app-header nav a.router-link-exact-active {
  background-color: rgba(255, 255, 255, 0.2);
}

.sub-app-content {
  padding: 20px;
  min-height: 300px;
}
</style> 