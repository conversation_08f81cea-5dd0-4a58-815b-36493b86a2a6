import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    userInfo: {
      name: '张三',
      role: '开发者',
      avatar: '👨‍💻'
    },
    tasks: [
      { id: 1, title: '完成微前端架构', completed: true },
      { id: 2, title: '编写文档', completed: false },
      { id: 3, title: '代码审查', completed: false }
    ],
    statistics: {
      totalTasks: 3,
      completedTasks: 1,
      pendingTasks: 2
    }
  },
  getters: {
    userInfo: state => state.userInfo,
    tasks: state => state.tasks,
    completedTasks: state => state.tasks.filter(task => task.completed),
    pendingTasks: state => state.tasks.filter(task => !task.completed),
    statistics: state => state.statistics
  },
  mutations: {
    UPDATE_USER_INFO(state, userInfo) {
      state.userInfo = { ...state.userInfo, ...userInfo }
    },
    TOGGLE_TASK(state, taskId) {
      const task = state.tasks.find(t => t.id === taskId)
      if (task) {
        task.completed = !task.completed
        // 更新统计信息
        state.statistics.completedTasks = state.tasks.filter(t => t.completed).length
        state.statistics.pendingTasks = state.tasks.filter(t => !t.completed).length
      }
    },
    ADD_TASK(state, task) {
      state.tasks.push({
        id: Date.now(),
        title: task.title,
        completed: false
      })
      state.statistics.totalTasks = state.tasks.length
      state.statistics.pendingTasks = state.tasks.filter(t => !t.completed).length
    }
  },
  actions: {
    updateUserInfo({ commit }, userInfo) {
      commit('UPDATE_USER_INFO', userInfo)
    },
    toggleTask({ commit }, taskId) {
      commit('TOGGLE_TASK', taskId)
    },
    addTask({ commit }, task) {
      commit('ADD_TASK', task)
    }
  },
  modules: {
  }
}) 