<template>
  <div class="dashboard">
    <h3>仪表板</h3>
    
    <div class="dashboard-grid">
      <div class="tasks-section">
        <h4>任务管理</h4>
        <div class="add-task">
          <input 
            v-model="newTaskTitle" 
            @keyup.enter="addNewTask"
            placeholder="输入新任务..."
            class="task-input"
          >
          <button @click="addNewTask" class="add-btn">添加</button>
        </div>
        
        <div class="task-list">
          <div 
            v-for="task in tasks" 
            :key="task.id" 
            class="task-item"
            :class="{ completed: task.completed }"
          >
            <input 
              type="checkbox" 
              :checked="task.completed"
              @change="toggleTask(task.id)"
              class="task-checkbox"
            >
            <span class="task-title">{{ task.title }}</span>
            <span class="task-status-badge" :class="task.completed ? 'completed' : 'pending'">
              {{ task.completed ? '已完成' : '进行中' }}
            </span>
          </div>
        </div>
      </div>

      <div class="stats-section">
        <h4>统计信息</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.totalTasks }}</div>
            <div class="stat-label">总任务</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ statistics.completedTasks }}</div>
            <div class="stat-label">已完成</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ statistics.pendingTasks }}</div>
            <div class="stat-label">待处理</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ completionRate }}%</div>
            <div class="stat-label">完成率</div>
          </div>
        </div>
        
        <div class="progress-section">
          <h5>进度条</h5>
          <div class="progress-bar">
            <div 
              class="progress-fill" 
              :style="{ width: completionRate + '%' }"
            ></div>
          </div>
          <p class="progress-text">{{ completionRate }}% 完成</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'Dashboard-Page',
  data() {
    return {
      newTaskTitle: ''
    }
  },
  computed: {
    ...mapGetters(['tasks', 'statistics']),
    completionRate() {
      if (this.statistics.totalTasks === 0) return 0
      return Math.round((this.statistics.completedTasks / this.statistics.totalTasks) * 100)
    }
  },
  methods: {
    ...mapActions(['toggleTask', 'addTask']),
    addNewTask() {
      if (this.newTaskTitle.trim()) {
        this.addTask({ title: this.newTaskTitle.trim() })
        this.newTaskTitle = ''
      }
    }
  }
}
</script>

<style scoped>
.dashboard {
  max-width: 1000px;
  margin: 0 auto;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
}

.tasks-section, .stats-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.tasks-section h4, .stats-section h4 {
  margin: 0 0 20px 0;
  color: #333;
}

.add-task {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.task-input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
}

.add-btn {
  padding: 10px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: opacity 0.3s;
}

.add-btn:hover {
  opacity: 0.9;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.task-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.task-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.task-item.completed {
  opacity: 0.7;
}

.task-checkbox {
  margin-right: 15px;
  transform: scale(1.2);
}

.task-title {
  flex: 1;
  font-size: 14px;
}

.task-item.completed .task-title {
  text-decoration: line-through;
}

.task-status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.task-status-badge.completed {
  background: #d4edda;
  color: #155724;
}

.task-status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
}

.stat-item {
  background: white;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
}

.progress-section {
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-section h5 {
  margin: 0 0 10px 0;
  color: #333;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.progress-text {
  margin: 0;
  font-size: 12px;
  color: #666;
  text-align: center;
}
</style> 