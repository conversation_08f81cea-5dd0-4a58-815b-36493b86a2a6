<template>
  <div class="profile">
    <h3>个人资料</h3>
    
    <div class="profile-content">
      <div class="profile-card">
        <div class="avatar-section">
          <div class="avatar">{{ userInfo.avatar }}</div>
          <h4>{{ userInfo.name }}</h4>
          <p class="role">{{ userInfo.role }}</p>
        </div>
        
        <div class="info-section">
          <h5>基本信息</h5>
          <div class="info-grid">
            <div class="info-item">
              <label>姓名</label>
              <input 
                v-model="editableUserInfo.name" 
                :disabled="!isEditing"
                class="info-input"
              >
            </div>
            <div class="info-item">
              <label>角色</label>
              <select 
                v-model="editableUserInfo.role" 
                :disabled="!isEditing"
                class="info-input"
              >
                <option value="开发者">开发者</option>
                <option value="设计师">设计师</option>
                <option value="产品经理">产品经理</option>
                <option value="测试工程师">测试工程师</option>
              </select>
            </div>
            <div class="info-item">
              <label>头像</label>
              <select 
                v-model="editableUserInfo.avatar" 
                :disabled="!isEditing"
                class="info-input"
              >
                <option value="👨‍💻">👨‍💻 开发者</option>
                <option value="👩‍🎨">👩‍🎨 设计师</option>
                <option value="👨‍💼">👨‍💼 经理</option>
                <option value="👩‍🔬">👩‍🔬 测试</option>
                <option value="🧑‍💻">🧑‍💻 程序员</option>
              </select>
            </div>
          </div>
          
          <div class="action-buttons">
            <button 
              v-if="!isEditing"
              @click="startEditing"
              class="btn btn-primary"
            >
              编辑资料
            </button>
            <template v-else>
              <button 
                @click="saveChanges"
                class="btn btn-success"
              >
                保存更改
              </button>
              <button 
                @click="cancelEditing"
                class="btn btn-secondary"
              >
                取消
              </button>
            </template>
          </div>
        </div>
      </div>
      
      <div class="activity-section">
        <h5>活动统计</h5>
        <div class="activity-stats">
          <div class="activity-item">
            <div class="activity-icon">📋</div>
            <div class="activity-content">
              <div class="activity-number">{{ statistics.totalTasks }}</div>
              <div class="activity-label">创建的任务</div>
            </div>
          </div>
          <div class="activity-item">
            <div class="activity-icon">✅</div>
            <div class="activity-content">
              <div class="activity-number">{{ statistics.completedTasks }}</div>
              <div class="activity-label">完成的任务</div>
            </div>
          </div>
          <div class="activity-item">
            <div class="activity-icon">⏳</div>
            <div class="activity-content">
              <div class="activity-number">{{ statistics.pendingTasks }}</div>
              <div class="activity-label">待处理任务</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'Profile-Page',
  data() {
    return {
      isEditing: false,
      editableUserInfo: {}
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'statistics'])
  },
  methods: {
    ...mapActions(['updateUserInfo']),
    startEditing() {
      this.isEditing = true
      this.editableUserInfo = { ...this.userInfo }
    },
    saveChanges() {
      this.updateUserInfo(this.editableUserInfo)
      this.isEditing = false
    },
    cancelEditing() {
      this.isEditing = false
      this.editableUserInfo = { ...this.userInfo }
    }
  },
  created() {
    this.editableUserInfo = { ...this.userInfo }
  }
}
</script>

<style scoped>
.profile {
  max-width: 800px;
  margin: 0 auto;
}

.profile-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
}

.profile-card {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.avatar-section {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #dee2e6;
}

.avatar {
  font-size: 4rem;
  margin-bottom: 15px;
}

.avatar-section h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 1.5rem;
}

.role {
  margin: 0;
  color: #666;
  font-size: 1rem;
}

.info-section h5 {
  margin: 0 0 20px 0;
  color: #333;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.info-item label {
  font-weight: bold;
  color: #555;
  font-size: 14px;
}

.info-input {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
  background: white;
}

.info-input:disabled {
  background: #f8f9fa;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: opacity 0.3s;
}

.btn:hover {
  opacity: 0.9;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.activity-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.activity-section h5 {
  margin: 0 0 20px 0;
  color: #333;
}

.activity-stats {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.activity-item {
  display: flex;
  align-items: center;
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.activity-icon {
  font-size: 2rem;
  margin-right: 15px;
}

.activity-content {
  flex: 1;
}

.activity-number {
  font-size: 1.5rem;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 2px;
}

.activity-label {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
}
</style> 