<template>
  <div class="home">
    <h3>子应用首页</h3>
    <div class="welcome-section">
      <div class="user-welcome">
        <span class="avatar">{{ userInfo.avatar }}</span>
        <div class="user-text">
          <h4>欢迎回来，{{ userInfo.name }}！</h4>
          <p>您的角色：{{ userInfo.role }}</p>
        </div>
      </div>
      
      <div class="quick-stats">
        <div class="stat-card">
          <h4>{{ statistics.totalTasks }}</h4>
          <p>总任务数</p>
        </div>
        <div class="stat-card">
          <h4>{{ statistics.completedTasks }}</h4>
          <p>已完成</p>
        </div>
        <div class="stat-card">
          <h4>{{ statistics.pendingTasks }}</h4>
          <p>待处理</p>
        </div>
      </div>
    </div>

    <div class="recent-tasks">
      <h4>最近任务</h4>
      <div class="task-list">
        <div 
          v-for="task in tasks.slice(0, 3)" 
          :key="task.id" 
          class="task-item"
          :class="{ completed: task.completed }"
        >
          <span class="task-status">
            {{ task.completed ? '✅' : '⏳' }}
          </span>
          <span class="task-title">{{ task.title }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Home-Page',
  computed: {
    ...mapGetters(['userInfo', 'tasks', 'statistics'])
  }
}
</script>

<style scoped>
.home {
  max-width: 800px;
  margin: 0 auto;
}

.welcome-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.user-welcome {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.avatar {
  font-size: 3rem;
  margin-right: 15px;
}

.user-text h4 {
  margin: 0 0 5px 0;
  color: #333;
}

.user-text p {
  margin: 0;
  color: #666;
}

.quick-stats {
  display: flex;
  gap: 10px;
}

.stat-card {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
}

.stat-card h4 {
  margin: 0 0 5px 0;
  font-size: 1.5rem;
}

.stat-card p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

.recent-tasks {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.recent-tasks h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.task-item {
  display: flex;
  align-items: center;
  padding: 10px;
  background: white;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.task-item.completed {
  opacity: 0.7;
}

.task-status {
  margin-right: 10px;
  font-size: 1.2rem;
}

.task-title {
  flex: 1;
}

.task-item.completed .task-title {
  text-decoration: line-through;
}
</style> 