# qiankun 微前端应用程序

这是一个基于 qiankun 的微前端应用程序示例，包含一个主应用和一个子应用，使用 Vue2 + Vue Router + Vuex 技术栈。

## 项目结构

```
qiankun-microfrontend/
├── main-application/          # 主应用
│   ├── src/
│   │   ├── views/            # 页面组件
│   │   ├── router/           # 路由配置
│   │   ├── store/            # Vuex状态管理
│   │   ├── App.vue           # 根组件
│   │   └── main.js           # 入口文件
│   ├── public/
│   ├── package.json
│   └── vue.config.js
├── sub-application/           # 子应用
│   ├── src/
│   │   ├── views/            # 页面组件
│   │   ├── router/           # 路由配置
│   │   ├── store/            # Vuex状态管理
│   │   ├── App.vue           # 根组件
│   │   └── main.js           # 入口文件（包含qiankun生命周期）
│   ├── public/
│   ├── package.json
│   └── vue.config.js
├── package.json               # 根项目配置
└── README.md
```

## 技术栈

- **主应用**: Vue 2 + Vue Router + Vuex + qiankun
- **子应用**: Vue 2 + Vue Router + Vuex
- **微前端框架**: qiankun

## 功能特性

### 主应用功能
- 🏠 首页展示
- 📖 关于页面
- 🎯 子应用容器
- 🔄 应用间通信
- 📊 全局状态管理

### 子应用功能
- 🏠 子应用首页
- 📊 仪表板（任务管理）
- 👤 个人资料管理
- ✅ 任务状态切换
- 📈 统计信息展示

## 安装和运行

### 1. 安装依赖

```bash
# 安装所有应用的依赖
npm run install:all

# 或者分别安装
npm install
cd main-application && npm install
cd ../sub-application && npm install
```

### 2. 启动应用

```bash
# 同时启动主应用和子应用
npm run dev

# 或者分别启动
npm run dev:main    # 主应用 - http://localhost:8080
npm run dev:sub     # 子应用 - http://localhost:8081
```

### 3. 访问应用

- 主应用: http://localhost:8080
- 子应用: http://localhost:8081
- 在主应用中访问子应用: http://localhost:8080/sub-app

## 应用配置

### 主应用配置

主应用在 `main-application/src/main.js` 中注册了子应用：

```javascript
registerMicroApps([
  {
    name: 'sub-application',
    entry: '//localhost:8081',
    container: '#subapp-container',
    activeRule: '/sub-app',
  },
])
```

### 子应用配置

子应用在 `sub-application/vue.config.js` 中配置了qiankun所需的webpack设置：

```javascript
configureWebpack: {
  output: {
    library: `${name}-[name]`,
    libraryTarget: 'umd',
    jsonpFunction: `webpackJsonp_${name}`,
  },
}
```

## 构建部署

```bash
# 构建所有应用
npm run build

# 分别构建
npm run build:main
npm run build:sub
```

## 开发说明

### 路由配置

- 主应用使用标准的Vue Router配置
- 子应用需要根据是否在qiankun环境中运行来设置base路径：

```javascript
const router = new VueRouter({
  mode: 'history',
  base: window.__POWERED_BY_QIANKUN__ ? '/sub-app' : process.env.BASE_URL,
  routes
})
```

### 生命周期函数

子应用必须导出qiankun要求的生命周期函数：

```javascript
export async function bootstrap() {
  console.log('[vue] sub-application bootstraped')
}

export async function mount(props) {
  console.log('[vue] props from main framework', props)
  render(props)
}

export async function unmount() {
  instance.$destroy()
  instance.$el.innerHTML = ''
  instance = null
}
```

## 应用间通信

可以通过qiankun提供的通信机制实现主应用和子应用之间的数据传递和事件通信。

## 故障排除

1. **跨域问题**: 确保子应用的devServer配置了CORS头部
2. **路由问题**: 检查子应用的base路径配置
3. **资源加载**: 确保webpack配置正确输出UMD格式

## 扩展开发

要添加新的子应用：

1. 创建新的Vue应用
2. 配置webpack输出为UMD格式
3. 添加qiankun生命周期函数
4. 在主应用中注册新的子应用

## 许可证

MIT License 