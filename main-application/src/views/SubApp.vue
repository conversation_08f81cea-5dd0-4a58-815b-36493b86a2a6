<template>
  <div class="sub-app">
    <h2>子应用页面</h2>
    <div class="sub-app-info">
      <p>当前页面将加载子应用，子应用会在下方容器中显示。</p>
      <p>子应用运行在端口 8081，路由前缀为 /sub-app</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SubApp'
}
</script>

<style scoped>
.sub-app {
  max-width: 800px;
  margin: 0 auto;
}

.sub-app-info {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.sub-app-info p {
  margin: 10px 0;
  color: #666;
}
</style> 