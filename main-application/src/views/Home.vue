<template>
  <div class="home">
    <h2>主应用首页</h2>
    <div class="welcome-card">
      <h3>{{ globalMessage }}</h3>
      <p>这是一个基于qiankun的微前端主应用示例</p>
      <div class="features">
        <div class="feature">
          <h4>🚀 技术栈</h4>
          <ul>
            <li>Vue 2</li>
            <li>Vue Router</li>
            <li>Vuex</li>
            <li>qiankun</li>
          </ul>
        </div>
        <div class="feature">
          <h4>📦 功能特性</h4>
          <ul>
            <li>微应用注册</li>
            <li>路由管理</li>
            <li>状态共享</li>
            <li>应用通信</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Home-Page',
  computed: {
    ...mapGetters(['globalMessage'])
  }
}
</script>

<style scoped>
.home {
  max-width: 800px;
  margin: 0 auto;
}

.welcome-card {
  background: #f9f9f9;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.welcome-card h3 {
  color: #42b983;
  margin-bottom: 20px;
}

.features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-top: 30px;
}

.feature {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

.feature h4 {
  margin-bottom: 15px;
  color: #333;
}

.feature ul {
  list-style: none;
  padding: 0;
}

.feature li {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.feature li:last-child {
  border-bottom: none;
}
</style> 