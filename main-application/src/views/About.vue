<template>
  <div class="about">
    <h2>关于页面</h2>
    <div class="about-content">
      <h3>qiankun 微前端架构</h3>
      <p>qiankun 是一个基于 single-spa 的微前端实现库，旨在帮助大家能更简单、无痛的构建一个生产可用微前端架构系统。</p>
      
      <div class="architecture">
        <h4>架构特点：</h4>
        <ul>
          <li>📦 基于 single-spa 封装，提供了更加开箱即用的 API</li>
          <li>📱 技术栈无关，任意技术栈的应用均可使用/接入</li>
          <li>💪 HTML Entry 接入方式，让你接入微应用像使用 iframe 一样简单</li>
          <li>🛡 样式隔离，确保微应用之间样式互相不干扰</li>
          <li>🧳 JS 沙箱，确保微应用之间全局变量/事件不冲突</li>
          <li>⚡ 资源预加载，在浏览器空闲时间预加载未打开的微应用资源</li>
        </ul>
      </div>

      <div class="current-state">
        <h4>当前状态：</h4>
        <p>用户登录状态: {{ isLoggedIn ? '已登录' : '未登录' }}</p>
        <p>全局消息: {{ globalMessage }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'About-Page',
  computed: {
    ...mapGetters(['isLoggedIn', 'globalMessage'])
  }
}
</script>

<style scoped>
.about {
  max-width: 800px;
  margin: 0 auto;
}

.about-content {
  background: #f9f9f9;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.about-content h3 {
  color: #42b983;
  margin-bottom: 20px;
}

.architecture, .current-state {
  margin-top: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

.architecture h4, .current-state h4 {
  color: #333;
  margin-bottom: 15px;
}

.architecture ul {
  list-style: none;
  padding: 0;
}

.architecture li {
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.architecture li:last-child {
  border-bottom: none;
}

.current-state p {
  margin: 10px 0;
  padding: 10px;
  background: #f0f0f0;
  border-radius: 5px;
}
</style> 