import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    user: null,
    globalMessage: '欢迎使用qiankun微前端应用'
  },
  getters: {
    isLoggedIn: state => !!state.user,
    globalMessage: state => state.globalMessage
  },
  mutations: {
    SET_USER(state, user) {
      state.user = user
    },
    SET_GLOBAL_MESSAGE(state, message) {
      state.globalMessage = message
    }
  },
  actions: {
    setUser({ commit }, user) {
      commit('SET_USER', user)
    },
    setGlobalMessage({ commit }, message) {
      commit('SET_GLOBAL_MESSAGE', message)
    }
  },
  modules: {
  }
}) 