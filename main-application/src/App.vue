<template>
  <div id="app">
    <div class="header">
      <h1>qiankun 微前端主应用</h1>
      <nav>
        <router-link to="/">首页</router-link>
        <router-link to="/about">关于</router-link>
        <router-link to="/jwgkApp">子应用</router-link>
      </nav>
    </div>
    
    <div class="main-content">
      <router-view/>
      <!-- 子应用容器 -->
      <div id="subapp-container"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

.header {
  background-color: #42b983;
  padding: 20px;
  color: white;
}

.header h1 {
  margin: 0 0 20px 0;
}

.header nav {
  display: flex;
  gap: 20px;
}

.header nav a {
  color: white;
  text-decoration: none;
  padding: 10px 15px;
  border-radius: 5px;
  transition: background-color 0.3s;
}

.header nav a:hover,
.header nav a.router-link-exact-active {
  background-color: rgba(255, 255, 255, 0.2);
}

.main-content {
  /* padding: 20px; */
}

#subapp-container {
  width: 100%;
  height: 700px;  /* 最小高度 */
  position: relative;
  overflow: hidden;     /* 超出时显示滚动条 */
  box-sizing: border-box;
}
[data-name="sub-application"] {
  width: 100%;
  height: 100%;
  overflow: hidden;
}


/* 响应式设计 */
@media (max-width: 768px) {
  #subapp-container {
    min-height: 400px;
    max-height: 70vh;
  }
}
</style> 