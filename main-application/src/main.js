import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import { registerMicroApps, start } from 'qiankun'

Vue.config.productionTip = false

// 注册微应用
registerMicroApps([
  {
    name: 'sub-application',
    entry: 'http://localhost:1234/',
    container: '#subapp-container',
    activeRule: '/#/jwgkApp',
  },
])

// 启动 qiankun
start()

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app') 